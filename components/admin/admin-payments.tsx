"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

// 模拟缴费数据
type Payment = {
  id: string
  userId: string
  userName: string
  userEmail: string
  amount: number
  currency: string
  paymentMethod: string
  status: 'pending' | 'approved' | 'rejected' | 'refunded'
  submittedAt: string
  reviewedAt?: string
  reviewedBy?: string
  notes?: string
  receiptUrl?: string
}

const mockPayments: Payment[] = [
  {
    id: "PAY001",
    userId: "user1",
    userName: "<PERSON>",
    userEmail: "<EMAIL>",
    amount: 350.00,
    currency: "USD",
    paymentMethod: "Bank Transfer",
    status: "pending",
    submittedAt: "2024-01-20",
    receiptUrl: "/receipts/pay001.pdf"
  },
  {
    id: "PAY002",
    userId: "user2",
    userName: "Jane Smith",
    userEmail: "<EMAIL>",
    amount: 350.00,
    currency: "USD",
    paymentMethod: "Credit Card",
    status: "approved",
    submittedAt: "2024-01-18",
    reviewedAt: "2024-01-19",
    reviewedBy: "Admin User",
    notes: "Payment verified successfully"
  },
  {
    id: "PAY003",
    userId: "user3",
    userName: "Bob Johnson",
    userEmail: "<EMAIL>",
    amount: 280.00,
    currency: "USD",
    paymentMethod: "Bank Transfer",
    status: "pending",
    submittedAt: "2024-01-22",
    receiptUrl: "/receipts/pay003.pdf"
  },
  {
    id: "PAY004",
    userId: "user4",
    userName: "Alice Brown",
    userEmail: "<EMAIL>",
    amount: 350.00,
    currency: "USD",
    paymentMethod: "PayPal",
    status: "rejected",
    submittedAt: "2024-01-15",
    reviewedAt: "2024-01-16",
    reviewedBy: "Admin User",
    notes: "Invalid receipt provided"
  }
]

export default function AdminPaymentsPage() {
  const [payments, setPayments] = useState(mockPayments)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")

  const filteredPayments = payments.filter(payment => {
    const matchesSearch = payment.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         payment.userEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         payment.id.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || payment.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const handleApprovePayment = (id: string) => {
    setPayments(payments.map(payment =>
      payment.id === id
        ? {
            ...payment,
            status: "approved" as const,
            reviewedAt: new Date().toISOString().split('T')[0],
            reviewedBy: "Current Admin"
          }
        : payment
    ))
  }

  const handleRejectPayment = (id: string, notes: string) => {
    setPayments(payments.map(payment =>
      payment.id === id
        ? {
            ...payment,
            status: "rejected" as const,
            reviewedAt: new Date().toISOString().split('T')[0],
            reviewedBy: "Current Admin",
            notes
          }
        : payment
    ))
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-amber-50 text-amber-700 border-amber-200'
      case 'approved': return 'bg-green-50 text-green-700 border-green-200'
      case 'rejected': return 'bg-red-50 text-red-700 border-red-200'
      case 'refunded': return 'bg-blue-50 text-blue-700 border-blue-200'
      default: return 'bg-gray-50 text-gray-700 border-gray-200'
    }
  }

  const pendingCount = payments.filter(p => p.status === 'pending').length
  const approvedCount = payments.filter(p => p.status === 'approved').length
  const rejectedCount = payments.filter(p => p.status === 'rejected').length
  const totalAmount = payments.filter(p => p.status === 'approved').reduce((sum, p) => sum + p.amount, 0)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Payment Review</h1>
          <p className="text-gray-600">Review and approve conference registration payments</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Pending Review</p>
                <h3 className="text-2xl font-bold text-gray-900 mt-1">{pendingCount}</h3>
              </div>
              <div className="w-12 h-12 bg-amber-100 rounded-full flex items-center justify-center">
                <i className="fas fa-clock text-amber-600 text-xl"></i>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Approved</p>
                <h3 className="text-2xl font-bold text-gray-900 mt-1">{approvedCount}</h3>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                <i className="fas fa-check text-green-600 text-xl"></i>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Rejected</p>
                <h3 className="text-2xl font-bold text-gray-900 mt-1">{rejectedCount}</h3>
              </div>
              <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                <i className="fas fa-times text-red-600 text-xl"></i>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Revenue</p>
                <h3 className="text-2xl font-bold text-gray-900 mt-1">${totalAmount.toFixed(2)}</h3>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                <i className="fas fa-dollar-sign text-blue-600 text-xl"></i>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="all" className="w-full">
        <TabsList>
          <TabsTrigger value="all">All Payments</TabsTrigger>
          <TabsTrigger value="pending">Pending Review ({pendingCount})</TabsTrigger>
          <TabsTrigger value="approved">Approved</TabsTrigger>
          <TabsTrigger value="rejected">Rejected</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-6">
          {/* Search and Filters */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="flex-1">
                  <Input
                    placeholder="Search by name, email, or payment ID..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full"
                  />
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="approved">Approved</SelectItem>
                    <SelectItem value="rejected">Rejected</SelectItem>
                    <SelectItem value="refunded">Refunded</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Payments List */}
          <Card>
            <CardHeader>
              <CardTitle>Payment Records ({filteredPayments.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-medium text-gray-600">Payment ID</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-600">User</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-600">Amount</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-600">Method</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-600">Status</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-600">Submitted</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-600">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredPayments.map((payment) => (
                      <tr key={payment.id} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="py-3 px-4 font-mono text-sm">{payment.id}</td>
                        <td className="py-3 px-4">
                          <div>
                            <div className="font-medium text-gray-900">{payment.userName}</div>
                            <div className="text-sm text-gray-500">{payment.userEmail}</div>
                          </div>
                        </td>
                        <td className="py-3 px-4 font-semibold">
                          {payment.currency} ${payment.amount.toFixed(2)}
                        </td>
                        <td className="py-3 px-4">{payment.paymentMethod}</td>
                        <td className="py-3 px-4">
                          <Badge variant="outline" className={getStatusColor(payment.status)}>
                            {payment.status}
                          </Badge>
                        </td>
                        <td className="py-3 px-4">{payment.submittedAt}</td>
                        <td className="py-3 px-4">
                          <div className="flex space-x-2">
                            {payment.receiptUrl && (
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                <i className="fas fa-file-pdf text-red-500"></i>
                              </Button>
                            )}
                            {payment.status === 'pending' && (
                              <>
                                <Button
                                  size="sm"
                                  onClick={() => handleApprovePayment(payment.id)}
                                  className="bg-green-600 hover:bg-green-700"
                                >
                                  <i className="fas fa-check mr-1"></i>
                                  Approve
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleRejectPayment(payment.id, "Requires review")}
                                  className="border-red-300 text-red-700 hover:bg-red-50"
                                >
                                  <i className="fas fa-times mr-1"></i>
                                  Reject
                                </Button>
                              </>
                            )}
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <i className="fas fa-eye text-gray-500"></i>
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="pending" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Pending Payments ({pendingCount})</CardTitle>
              <CardDescription>Payments awaiting review and approval</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {payments.filter(p => p.status === 'pending').map((payment) => (
                  <div key={payment.id} className="border border-amber-200 bg-amber-50 rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h3 className="font-semibold text-gray-900">{payment.userName}</h3>
                          <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-300">
                            {payment.paymentMethod}
                          </Badge>
                        </div>
                        <p className="text-gray-600 mb-1">{payment.userEmail}</p>
                        <p className="text-lg font-semibold text-gray-900 mb-2">
                          {payment.currency} ${payment.amount.toFixed(2)}
                        </p>
                        <p className="text-sm text-gray-500">
                          Submitted: {payment.submittedAt} • ID: {payment.id}
                        </p>
                      </div>
                      <div className="flex space-x-2 ml-4">
                        {payment.receiptUrl && (
                          <Button variant="outline" size="sm">
                            <i className="fas fa-file-pdf mr-2"></i>
                            View Receipt
                          </Button>
                        )}
                        <Button
                          size="sm"
                          onClick={() => handleApprovePayment(payment.id)}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <i className="fas fa-check mr-2"></i>
                          Approve
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleRejectPayment(payment.id, "Requires review")}
                          className="border-red-300 text-red-700 hover:bg-red-50"
                        >
                          <i className="fas fa-times mr-2"></i>
                          Reject
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="approved">
          <Card>
            <CardHeader>
              <CardTitle>Approved Payments ({approvedCount})</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Approved payments will be displayed here.</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="rejected">
          <Card>
            <CardHeader>
              <CardTitle>Rejected Payments ({rejectedCount})</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Rejected payments will be displayed here.</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

"use client"

import { useState, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { motion } from "framer-motion"

// 模拟用户类型
type User = {
  id: string
  name: string
  email: string
  role: 'user' | 'admin'
}

// 侧边栏菜单项
const sidebarItems = [
  {
    name: "Dashboard",
    href: "/admin",
    icon: "fas fa-tachometer-alt"
  },
  {
    name: "Users",
    href: "/admin/users",
    icon: "fas fa-users"
  },
  {
    name: "Speakers",
    href: "/admin/speakers",
    icon: "fas fa-microphone"
  },
  {
    name: "Content",
    href: "/admin/content",
    icon: "fas fa-file-alt"
  },
  {
    name: "Settings",
    href: "/admin/settings",
    icon: "fas fa-cog"
  }
]

interface AdminLayoutProps {
  children: React.ReactNode
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const [user, setUser] = useState<User | null>(null)
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const pathname = usePathname()

  useEffect(() => {
    console.log('[AdminLayout] useEffect started.'); // 新增日志
    // 检查用户权限
    const userData = localStorage.getItem('user');
    console.log('[AdminLayout] Raw user data from localStorage:', userData); // 修改日志
    
    if (userData) {
      try {
        // 添加更健壮的JSON解析错误处理
        let parsedData;
        try {
          parsedData = JSON.parse(userData) as any;
          console.log('解析后的数据:', parsedData);
        } catch (jsonError) {
          console.error('[AdminLayout] Invalid JSON in localStorage:', jsonError);
          console.log('[AdminLayout] Raw userData that caused JSON error:', userData);
          alert('用户数据格式无效，请重新登录');
          console.log('[AdminLayout] Redirecting to /login due to invalid JSON.'); // 新增日志
          window.location.href = '/login';
          return;
        }
        
        // 提取用户信息，支持不同的数据结构
        const userInfo = parsedData.user_info || parsedData.data?.user_info || parsedData;
        console.log('提取的用户信息:', userInfo);
        console.log('用户角色:', userInfo.role);
        
        // 构建标准化的用户对象
        const standardUser: User = {
          id: userInfo.id?.toString() || '',
          name: userInfo.name || '',
          email: userInfo.email || '',
          role: userInfo.role || 'user'
        };
        
        console.log('标准化用户对象:', standardUser);
        
        // 直接设置用户，不进行角色检查，在渲染时检查
        setUser(standardUser);
      } catch (error) {
        console.error('[AdminLayout] Error processing user data:', error);
        alert('处理用户数据时出错，请重新登录');
        console.log('[AdminLayout] Redirecting to /login due to error processing data.'); // 新增日志
        window.location.href = '/login';
        return;
      }
    } else {
      alert('未找到用户数据，请先登录');
      console.log('[AdminLayout] Redirecting to /login because no user data found.'); // 新增日志
      window.location.href = '/login';
      return;
    }
  }, [])

  const handleLogout = () => {
    localStorage.removeItem('user')
    window.location.href = '/'
  }

  // 显示加载状态，直到用户数据加载完成
  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card>
          <CardContent className="p-8 text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">加载中...</h1>
            <p className="text-gray-600 mb-4">正在验证您的访问权限</p>
          </CardContent>
        </Card>
      </div>
    )
  }
  
  // 调试信息 - 在页面上显示用户数据
  // 角色检查：如果用户不是管理员，则显示访问拒绝页面
  if (user.role !== 'admin') {
    console.error('Access Denied: User is not an admin. Role:', user.role);
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card>
          <CardContent className="p-8 text-center">
            <h1 className="text-2xl font-bold text-red-600 mb-4">访问被拒绝</h1>
            <p className="text-gray-600 mb-4">
              您没有管理员权限访问此页面。您的角色是: <span className="font-bold">{user.role}</span>.
            </p>
            <div className="mt-6 flex flex-col sm:flex-row justify-center space-y-2 sm:space-y-0 sm:space-x-2">
              <Link href="/dashboard">
                <Button variant="outline" className="w-full sm:w-auto">返回仪表盘</Button>
              </Link>
              <Button onClick={handleLogout} variant="destructive" className="w-full sm:w-auto">
                退出登录
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  console.log('渲染时的用户数据 (admin access granted):', user);

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <motion.div
        initial={{ x: -250 }}
        animate={{ x: sidebarOpen ? 0 : -200 }}
        transition={{ duration: 0.3 }}
        className={`${
          sidebarOpen ? 'w-64' : 'w-16'
        } bg-white border-r border-gray-200 shadow-sm transition-all duration-300 flex flex-col`}
      >
        {/* Sidebar Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            {sidebarOpen && (
              <div>
                <h1 className="text-lg font-bold text-gray-900">Admin Panel</h1>
                <p className="text-sm text-gray-500">IFMB 2025</p>
              </div>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="p-2"
            >
              <i className={`fas ${sidebarOpen ? 'fa-chevron-left' : 'fa-chevron-right'}`}></i>
            </Button>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4">
          <ul className="space-y-2">
            {sidebarItems.map((item) => (
              <li key={item.name}>
                <Link
                  href={item.href}
                  className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    pathname === item.href
                      ? 'bg-gray-900 text-white'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <i className={`${item.icon} ${sidebarOpen ? 'mr-3' : ''} w-5 text-center`}></i>
                  {sidebarOpen && <span>{item.name}</span>}
                </Link>
              </li>
            ))}
          </ul>
        </nav>

        {/* User Info */}
        <div className="p-4 border-t border-gray-200">
          {sidebarOpen ? (
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gray-900 rounded-full flex items-center justify-center text-white text-sm font-medium">
                  {user.name.charAt(0).toUpperCase()}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">{user.name}</p>
                  <p className="text-xs text-gray-500 truncate">{user.email}</p>
                </div>
              </div>
              <div className="space-y-1">
                <Link href="/" className="block">
                  <Button variant="outline" size="sm" className="w-full justify-start">
                    <i className="fas fa-home mr-2"></i>
                    Back to Site
                  </Button>
                </Link>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleLogout}
                  className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <i className="fas fa-sign-out-alt mr-2"></i>
                  Logout
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <div className="w-8 h-8 bg-gray-900 rounded-full flex items-center justify-center text-white text-sm font-medium mx-auto">
                {user.name.charAt(0).toUpperCase()}
              </div>
              <Link href="/" className="block">
                <Button variant="ghost" size="sm" className="w-full p-2">
                  <i className="fas fa-home"></i>
                </Button>
              </Link>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleLogout}
                className="w-full p-2 text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <i className="fas fa-sign-out-alt"></i>
              </Button>
            </div>
          )}
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Bar */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                {sidebarItems.find(item => item.href === pathname)?.name || 'Admin Panel'}
              </h2>
              <p className="text-sm text-gray-500">
                Manage your International Forum on Maize Biology 2025
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="outline" size="sm">
                <i className="fas fa-bell mr-2"></i>
                Notifications
              </Button>
            </div>
          </div>
        </div>

        {/* Page Content */}
        <main className="flex-1 overflow-auto p-6">
          {children}
        </main>
      </div>
    </div>
  )
}

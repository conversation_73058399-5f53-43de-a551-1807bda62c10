"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import Link from "next/link"
import { motion } from "framer-motion"

// 用户类型定义
type User = {
  id: string
  name: string
  email: string
  role: "user" | "admin"
  emailVerified?: boolean
  capacity?: string
  organization?: string
  country?: string
}

export default function UserDashboard() {
  const [user, setUser] = useState<User | null>(null)

  useEffect(() => {
    // 检查用户登录状态
    const userData = localStorage.getItem('user')
    if (userData) {
      try {
        // 添加更健壮的JSON解析错误处理
        let parsedData;
        try {
          parsedData = JSON.parse(userData) as any;
        } catch (jsonError) {
          console.error('Invalid JSON in localStorage:', jsonError);
          console.log('Raw userData:', userData);
          window.location.href = '/login';
          return;
        }
        
        // 提取用户信息，支持不同的数据结构
        const userInfo = parsedData.user_info || parsedData.data?.user_info || parsedData;
        
        // 构建标准化的用户对象
        const standardUser: User = {
          id: userInfo.id?.toString() || '',
          name: userInfo.name || '',
          email: userInfo.email || '',
          role: userInfo.role || 'user',
          emailVerified: userInfo.email_verified || false,
          capacity: userInfo.capacity || '',
          organization: userInfo.organization || '',
          country: userInfo.country || ''
        };
        
        setUser(standardUser);
      } catch (error) {
        console.error('Error processing user data:', error);
        window.location.href = '/login';
        return;
      }
    } else {
      window.location.href = '/login';
      return;
    }
  }, [])

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Please Login</h1>
          <p className="text-gray-600 mb-4">You need to login to access your dashboard.</p>
          <Link href="/login">
            <Button>Login</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      <div className="container mx-auto px-6 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Welcome back, {user.name}!</h1>
          <p className="text-gray-600">Manage your conference registration and profile</p>
        </motion.div>

        {/* Email Verification Notice */}
        {user.role !== 'admin' && !user.emailVerified && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mb-8"
          >
            <Card className="border-amber-200 bg-amber-50">
              <CardContent className="pt-6">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <i className="fas fa-exclamation-triangle text-amber-600 text-xl"></i>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-medium text-amber-800 mb-2">
                      Email Verification Required
                    </h3>
                    <p className="text-amber-700 mb-4">
                      Your email address has not been verified yet. To access all conference features and pages,
                      please verify your email address. You can only access the Dashboard and Profile pages until verification is complete.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-3">
                      <Button
                        variant="outline"
                        className="border-amber-300 text-amber-800 hover:bg-amber-100"
                        onClick={() => {
                          // TODO: Implement resend verification email
                          alert('Verification email sent! Please check your inbox.');
                        }}
                      >
                        <i className="fas fa-envelope mr-2"></i>
                        Resend Verification Email
                      </Button>
                      <Link href="/profile">
                        <Button variant="outline" className="border-amber-300 text-amber-800 hover:bg-amber-100">
                          <i className="fas fa-user mr-2"></i>
                          Go to Profile
                        </Button>
                      </Link>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Admin Debug Panel */}
        {user.role === 'admin' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mb-8"
          >
            <Card className="border-purple-200 bg-purple-50">
              <CardContent className="pt-6">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <i className="fas fa-bug text-purple-600 text-xl"></i>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-medium text-purple-800 mb-2">
                      Admin Debug Panel
                    </h3>
                    <p className="text-purple-700 mb-4">
                      您的角色是: <strong>{user.role}</strong>
                    </p>
                    <div className="flex flex-col sm:flex-row gap-3">
                      <Link href="/admin">
                        <Button className="bg-purple-600 hover:bg-purple-700 text-white">
                          <i className="fas fa-tachometer-alt mr-2"></i>
                          尝试访问管理页面
                        </Button>
                      </Link>
                      <Button 
                        onClick={() => {
                          const userData = localStorage.getItem('user');
                          alert('用户数据: ' + userData);
                          
                          try {
                            // 解析用户数据
                            const parsedData = JSON.parse(userData || '{}') as any;
                            const userInfo = parsedData.user_info || parsedData.data?.user_info || parsedData;
                            alert('解析后的角色: ' + (userInfo.role || '未找到'));
                          } catch (error) {
                            alert('解析错误: ' + error);
                          }
                        }}
                        variant="outline"
                        className="border-purple-300 text-purple-800 hover:bg-purple-100"
                      >
                        <i className="fas fa-search mr-2"></i>
                        检查用户数据
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Quick Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Payment Status</CardTitle>
              <i className="fas fa-credit-card text-green-600"></i>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">Pending</div>
              <p className="text-xs text-muted-foreground">Registration fee</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Submission Status</CardTitle>
              <i className="fas fa-file-alt text-blue-600"></i>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">Not Started</div>
              <p className="text-xs text-muted-foreground">Abstract submission</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Days Until Event</CardTitle>
              <i className="fas fa-calendar text-purple-600"></i>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">45</div>
              <p className="text-xs text-muted-foreground">IFMB 2025</p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Main Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          {user.role === 'admin' && (
            <div className="mb-4 flex justify-end">
              <a href="/admin" className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                <i className="fas fa-crown mr-2"></i>
                管理员控制台
              </a>
            </div>
          )}
          <Tabs defaultValue="profile" className="space-y-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="profile">Profile</TabsTrigger>
              <TabsTrigger value="submission">Submission</TabsTrigger>
              <TabsTrigger value="accommodation">Accommodation</TabsTrigger>
            </TabsList>

            {/* Profile Tab */}
            <TabsContent value="profile" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Profile Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Name</label>
                        <p className="mt-1 text-sm text-gray-900">{user.name}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Email</label>
                        <p className="mt-1 text-sm text-gray-900">{user.email}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Role</label>
                        <p className="mt-1 text-sm text-gray-900 capitalize">{user.role}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Email Verification</label>
                        <p className="mt-1 text-sm text-gray-900">
                          {user.emailVerified ? (
                            <span className="text-green-600 flex items-center">
                              <i className="fas fa-check-circle mr-1"></i> Verified
                            </span>
                          ) : (
                            <span className="text-amber-600 flex items-center">
                              <i className="fas fa-exclamation-circle mr-1"></i> Not Verified
                            </span>
                          )}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Capacity</label>
                        <p className="mt-1 text-sm text-gray-900">{user.capacity || 'Not specified'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Organization</label>
                        <p className="mt-1 text-sm text-gray-900">{user.organization || 'Not specified'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Country</label>
                        <p className="mt-1 text-sm text-gray-900">{user.country || 'Not specified'}</p>
                      </div>
                    </div>
                    <div className="pt-4">
                      <Link href="/profile">
                        <Button>
                          <i className="fas fa-edit mr-2"></i>
                          Edit Profile
                        </Button>
                      </Link>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Submission Tab */}
            <TabsContent value="submission" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Abstract Submission</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0 pt-0.5">
                          <i className="fas fa-info-circle text-blue-500"></i>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-blue-800">Submission Guidelines</h3>
                          <div className="mt-2 text-sm text-blue-700">
                            <p>Abstract submissions are now open for IFMB 2025. Please follow these guidelines:</p>
                            <ul className="list-disc pl-5 mt-1 space-y-1">
                              <li>Maximum 300 words</li>
                              <li>Include title, authors, and affiliations</li>
                              <li>Submission deadline: July 15, 2025</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="border border-gray-200 rounded-lg p-4">
                      <h3 className="text-lg font-medium mb-4">Submission Status</h3>
                      <div className="flex items-center space-x-2 mb-6">
                        <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
                          <i className="fas fa-file-alt text-gray-500"></i>
                        </div>
                        <div className="h-0.5 flex-1 bg-gray-200"></div>
                        <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
                          <i className="fas fa-check text-gray-500"></i>
                        </div>
                        <div className="h-0.5 flex-1 bg-gray-200"></div>
                        <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
                          <i className="fas fa-envelope text-gray-500"></i>
                        </div>
                      </div>
                      <div className="grid grid-cols-3 text-center text-sm">
                        <div>Prepare</div>
                        <div>Submit</div>
                        <div>Notification</div>
                      </div>
                    </div>
                    
                    <div className="flex justify-center">
                      <Link href="/dashboard/submission">
                        <Button>
                          <i className="fas fa-paper-plane mr-2"></i>
                          Start Submission
                        </Button>
                      </Link>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Accommodation Tab */}
            <TabsContent value="accommodation" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Accommodation Options</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0 pt-0.5">
                          <i className="fas fa-hotel text-amber-500"></i>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-amber-800">Accommodation Information</h3>
                          <div className="mt-2 text-sm text-amber-700">
                            <p>We have negotiated special rates with several hotels near the conference venue. Booking through our platform gives you access to these discounted rates.</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="border rounded-lg overflow-hidden">
                        <div className="h-40 bg-gray-200 flex items-center justify-center">
                          <i className="fas fa-hotel text-4xl text-gray-400"></i>
                        </div>
                        <div className="p-4">
                          <h3 className="font-medium mb-1">Conference Hotel</h3>
                          <p className="text-sm text-gray-500 mb-2">★★★★☆ | 0.2 km from venue</p>
                          <p className="text-sm mb-4">Special rate: $120/night</p>
                          <Button variant="outline" size="sm" className="w-full">
                            <i className="fas fa-info-circle mr-2"></i>
                            View Details
                          </Button>
                        </div>
                      </div>
                      
                      <div className="border rounded-lg overflow-hidden">
                        <div className="h-40 bg-gray-200 flex items-center justify-center">
                          <i className="fas fa-hotel text-4xl text-gray-400"></i>
                        </div>
                        <div className="p-4">
                          <h3 className="font-medium mb-1">Budget Option</h3>
                          <p className="text-sm text-gray-500 mb-2">★★★☆☆ | 1.5 km from venue</p>
                          <p className="text-sm mb-4">Special rate: $75/night</p>
                          <Button variant="outline" size="sm" className="w-full">
                            <i className="fas fa-info-circle mr-2"></i>
                            View Details
                          </Button>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex justify-center">
                      <Link href="/dashboard/accommodation">
                        <Button>
                          <i className="fas fa-bed mr-2"></i>
                          Book Accommodation
                        </Button>
                      </Link>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </motion.div>
      </div>
    </div>
  )
}

"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useEffect, useState } from "react"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu"

// 模拟用户类型
type User = {
  id: string
  name: string
  email: string
  avatar?: string
  role: 'user' | 'admin'
  emailVerified?: boolean
}

export default function Header() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [user, setUser] = useState<User | null>(null)
  const pathname = usePathname()

  // 检查当前页面是否为认证相关页面
  const isAuthPage = ["/register", "/login", "/forgot-password"].includes(pathname)

  // 检查当前页面是否需要不透明 header (verify-email 页面保持透明)
  const needsSolidHeader = isAuthPage || pathname?.startsWith("/profile") || pathname?.startsWith("/dashboard")

  // 模拟检查用户登录状态
  useEffect(() => {
    const checkUserStatus = () => {
      const userData = localStorage.getItem('user')
      if (userData) {
        try {
          // 添加更健壮的JSON解析错误处理
          let parsedData;
          try {
            parsedData = JSON.parse(userData) as any;
          } catch (jsonError) {
            console.error('Invalid JSON in localStorage:', jsonError);
            console.log('Raw userData:', userData);
            // 如果数据格式不正确，清除localStorage中的数据
            localStorage.removeItem('user');
            return;
          }
          
          const userInfo = parsedData.user_info || (parsedData.data && parsedData.data.user_info);
          
          if (userInfo && typeof userInfo === 'object' && 'id' in userInfo) {
            setUser(userInfo as User);
          } else {
            console.error('Invalid user data format');
            // 不要删除用户数据，可能只是格式不符合预期
            // localStorage.removeItem('user');
          }
        } catch (error) {
          console.error('Error parsing user data:', error)
          // 解析错误才考虑删除
          // localStorage.removeItem('user')
        }
      }
    }

    checkUserStatus()
  }, [])

  // 登出功能
  const handleLogout = () => {
    localStorage.removeItem('user')
    setUser(null)
    window.location.href = '/'
  }

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true)
      } else {
        setIsScrolled(false)
      }
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMobileMenuOpen(false)
  }, [pathname])

  const navItems = [
    { name: "Home", href: "/" },
    { name: "Organization", href: "/organization" },
    { name: "Executive", href: "/executive" },
    { name: "Speakers", href: "/reporters" },
    { name: "Transportation", href: "/transportation" },
  ]

  return (
    <header
      className={`fixed top-0 right-0 left-0 z-50 transition-all duration-300 ${
        isScrolled || needsSolidHeader ? "bg-white/95 py-3 shadow-md backdrop-blur-sm" : "bg-black/20 py-5 backdrop-blur-sm"
      }`}
    >
      <div className="container mx-auto flex items-center justify-between px-6">
        {/* Logo */}
        <Link href="/" className="flex items-center space-x-3">
          <div
            className={`flex h-10 w-10 items-center justify-center rounded-full border-2 bg-white ${
              isScrolled || needsSolidHeader ? "border-yellow-500" : "border-yellow-400"
            } overflow-hidden shadow-md transition-all duration-300 hover:scale-105`}
          >
            <Image src="/favicon.ico" alt="IFMB Logo" width={24} height={24} className="object-contain" />
          </div>
          <h1
            className={`font-bold tracking-tight ${
              isScrolled || needsSolidHeader ? "text-gray-800" : "text-white"
            } transition-colors duration-300`}
          >
            <span className="hidden sm:inline">International Forum on </span>
            <span
              className={isScrolled || needsSolidHeader ? "text-amber-600" : "text-amber-400"}
              style={{ whiteSpace: "nowrap" }}
            >
              Maize Biology
            </span>
          </h1>
        </Link>

        {/* Desktop Navigation */}
        <div className="hidden items-center space-x-8 lg:flex">
          <NavigationMenu>
            <NavigationMenuList>
              {navItems.map((item) => (
                <NavigationMenuItem key={item.name}>
                  <Link href={item.href} legacyBehavior passHref>
                    <NavigationMenuLink
                      className={`${navigationMenuTriggerStyle()} ${
                        isScrolled || needsSolidHeader
                          ? "text-gray-700 hover:text-amber-600 bg-transparent hover:bg-gray-100"
                          : "text-white/90 hover:text-white bg-transparent hover:bg-white/10"
                      } ${pathname === item.href ? "bg-amber-500/10 text-amber-600" : ""}`}
                    >
                      {item.name}
                    </NavigationMenuLink>
                  </Link>
                </NavigationMenuItem>
              ))}
            </NavigationMenuList>
          </NavigationMenu>

          {/* 用户状态显示 */}
          {user ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className={`flex items-center space-x-2 rounded-lg px-3 py-2 ${
                    isScrolled || needsSolidHeader ? "hover:bg-gray-100" : "hover:bg-white/10"
                  }`}
                >
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-600 text-white text-sm font-medium">
                    {user.name ? user.name.charAt(0).toUpperCase() : '?'}
                  </div>
                  <span className={`font-medium ${isScrolled || needsSolidHeader ? "text-gray-700" : "text-white"}`}>
                    {user.name || 'User'}
                  </span>
                  <i className={`fas fa-chevron-down text-xs ${isScrolled || needsSolidHeader ? "text-gray-500" : "text-white/70"}`}></i>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <div className="px-3 py-2">
                  <p className="text-sm font-medium">{user.name || 'User'}</p>
                  <p className="text-xs text-gray-500">{user.email || 'No email'}</p>
                  {user.role !== 'admin' && (
                    <div className="mt-1 flex items-center gap-1">
                      <div className={`w-2 h-2 rounded-full ${user.emailVerified ? 'bg-green-500' : 'bg-red-500'}`}></div>
                      <span className={`text-xs ${user.emailVerified ? 'text-green-600' : 'text-red-600'}`}>
                        {user.emailVerified ? 'Email Verified' : 'Email Unverified'}
                      </span>
                    </div>
                  )}
                </div>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/profile" className="cursor-pointer">
                    <i className="fas fa-user mr-2"></i>
                    Profile
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/dashboard" className="cursor-pointer">
                    <i className="fas fa-tachometer-alt mr-2"></i>
                    Dashboard
                  </Link>
                </DropdownMenuItem>
                {user.role === 'admin' && (
                  <DropdownMenuItem asChild>
                    <Link href="/admin" className="cursor-pointer">
                      <i className="fas fa-cog mr-2"></i>
                      Admin Panel
                    </Link>
                  </DropdownMenuItem>
                )}
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleLogout} className="cursor-pointer text-red-600">
                  <i className="fas fa-sign-out-alt mr-2"></i>
                  Logout
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <Link href="/login">
              <Button
                className={`cursor-pointer rounded-lg font-medium ${
                  isScrolled || needsSolidHeader
                    ? "bg-green-600 hover:bg-green-700 text-white"
                    : "bg-white text-green-600 hover:bg-gray-50"
                } transform px-6 py-2 shadow-md transition-all duration-300 hover:-translate-y-1`}
              >
                Login
              </Button>
            </Link>
          )}
        </nav>

        {/* Mobile Menu Button */}
        <button
          className={`lg:hidden ${isScrolled || needsSolidHeader ? "text-gray-700" : "text-white"}`}
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          aria-label="Toggle menu"
        >
          {isMobileMenuOpen ? <i className="fas fa-times text-2xl"></i> : <i className="fas fa-bars text-2xl"></i>}
        </button>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
          className="bg-white shadow-lg lg:hidden"
        >
          <div className="container mx-auto px-6 py-4">
            <nav className="flex flex-col space-y-4">
              {navItems.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`rounded-lg px-4 py-2 font-medium transition-colors ${
                    pathname === item.href ? "bg-green-50 text-green-700" : "text-gray-700 hover:bg-gray-50"
                  }`}
                >
                  {item.name}
                </Link>
              ))}

              {/* 移动端用户状态 */}
              {user ? (
                <div className="border-t border-gray-200 pt-4 mt-4">
                  <div className="flex items-center space-x-3 px-4 py-2 mb-3">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-green-600 text-white font-medium">
                      {user.name.charAt(0).toUpperCase()}
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{user.name}</p>
                      <p className="text-sm text-gray-500">{user.email}</p>
                      {user.role !== 'admin' && (
                        <div className="mt-1 flex items-center gap-1">
                          <div className={`w-2 h-2 rounded-full ${user.emailVerified ? 'bg-green-500' : 'bg-red-500'}`}></div>
                          <span className={`text-xs ${user.emailVerified ? 'text-green-600' : 'text-red-600'}`}>
                            {user.emailVerified ? 'Email Verified' : 'Email Unverified'}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                  <Link href="/profile" className="block rounded-lg px-4 py-2 text-gray-700 hover:bg-gray-50">
                    <i className="fas fa-user mr-2"></i>
                    Profile
                  </Link>
                  <Link href="/dashboard" className="block rounded-lg px-4 py-2 text-gray-700 hover:bg-gray-50">
                    <i className="fas fa-tachometer-alt mr-2"></i>
                    Dashboard
                  </Link>
                  {user.role === 'admin' && (
                    <Link href="/admin" className="block rounded-lg px-4 py-2 text-gray-700 hover:bg-gray-50">
                      <i className="fas fa-cog mr-2"></i>
                      Admin Panel
                    </Link>
                  )}
                  <button
                    onClick={handleLogout}
                    className="w-full text-left rounded-lg px-4 py-2 text-red-600 hover:bg-red-50"
                  >
                    <i className="fas fa-sign-out-alt mr-2"></i>
                    Logout
                  </button>
                </div>
              ) : (
                <div className="border-t border-gray-200 pt-4 mt-4">
                  <Link href="/login" className="w-full">
                    <Button className="w-full rounded-lg bg-green-600 py-3 text-white hover:bg-green-700">
                      Login
                    </Button>
                  </Link>
                </div>
              )}
            </nav>
          </div>
        </motion.div>
      )}
    </header>
  )
}

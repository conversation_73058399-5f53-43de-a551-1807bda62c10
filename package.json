{"name": "next-meeting", "version": "0.0.1", "private": true, "scripts": {"dev": "cross-env FORCE_COLOR=1 next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "prettier": "prettier --check \"**/*.{js,jsx,ts,tsx}\"", "prettier:fix": "prettier --write \"**/*.{js,jsx,ts,tsx}\"", "analyze": "cross-env ANALYZE=true pnpm run build", "storybook": "cross-env FORCE_COLOR=1 storybook dev -p 6006", "test-storybook": "cross-env FORCE_COLOR=1 test-storybook", "build-storybook": "cross-env FORCE_COLOR=1 storybook build", "test": "cross-env FORCE_COLOR=1 jest --passWithNoTests", "e2e:headless": "playwright test", "e2e:ui": "playwright test --ui", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "postinstall": "npx patch-package -y", "coupling-graph": "npx madge --extensions js,jsx,ts,tsx,css,md,mdx ./ --exclude '.next|tailwind.config.js|reset.d.ts|prettier.config.js|postcss.config.js|playwright.config.ts|next.config.js|next-env.d.ts|instrumentation.ts|e2e/|README.md|.storybook/|.eslintrc.js' --image graph.svg"}, "dependencies": {"@next/bundle-analyzer": "^15.3.3", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-form": "^0.1.7", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@semantic-release/changelog": "^6.0.3", "@semantic-release/commit-analyzer": "13.0.1", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^11.0.3", "@semantic-release/npm": "^12.0.1", "@semantic-release/release-notes-generator": "^14.0.3", "@t3-oss/env-nextjs": "^0.13.6", "@types/bcryptjs": "^3.0.0", "@vercel/otel": "^1.12.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "echarts": "^5.6.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.16.0", "lodash": "^4.17.21", "lucide-react": "^0.511.0", "next": "15.3.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-turnstile": "^1.1.4", "tailwind-merge": "^3.3.0", "tw-animate-css": "^1.3.3", "zod": "^3.25.49"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/plugin-syntax-flow": "^7.27.1", "@babel/plugin-transform-optional-chaining": "^7.27.1", "@babel/plugin-transform-react-jsx": "^7.27.1", "@eslint/eslintrc": "^3.3.1", "@jest/globals": "30.0.0-beta.3", "@next/eslint-plugin-next": "15.3.3", "@opentelemetry/api": "1.9.0", "@opentelemetry/resources": "2.0.1", "@opentelemetry/sdk-node": "0.202.0", "@opentelemetry/sdk-trace-node": "2.0.1", "@opentelemetry/semantic-conventions": "1.34.0", "@playwright/test": "^1.52.0", "@storybook/addon-controls": "^9.0.4", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-interactions": "^8.6.14", "@storybook/addon-links": "^9.0.4", "@storybook/blocks": "^8.6.14", "@storybook/nextjs": "^9.0.4", "@storybook/react": "^9.0.4", "@storybook/test": "^8.6.14", "@storybook/test-runner": "^0.22.0", "@tailwindcss/postcss": "^4.1.8", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@total-typescript/ts-reset": "^0.6.1", "@types/jest": "^29.5.14", "@types/node": "^22.15.29", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@typescript-eslint/eslint-plugin": "8.33.1", "all-contributors-cli": "^6.26.1", "cross-env": "^7.0.3", "eslint": "^9.28.0", "eslint-config-next": "15.3.3", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-storybook": "^9.0.4", "eslint-plugin-tailwindcss": "^3.18.0", "fetch-mock": "^12.5.2", "gzip-size": "7.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "30.0.0-beta.3", "mkdirp": "^3.0.1", "patch-package": "^8.0.0", "postcss": "^8.5.4", "postinstall-postinstall": "^2.1.0", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "^0.6.12", "semantic-release": "^24.2.5", "storybook": "^9.0.4", "tailwindcss": "^4.1.8", "ts-jest": "^29.3.4", "tsc": "^2.0.4", "typed-query-selector": "^2.12.0", "typescript": "^5.8.3", "typescript-eslint": "^8.33.1", "webpack": "5.99.9"}, "engines": {"node": ">=20.0.0"}, "packageManager": "pnpm@9.1.0"}
import "styles/tailwind.css"
import { Metada<PERSON> } from "next"
import { Inter } from "next/font/google"
import LayoutWrapper from "@/components/layout/layout-wrapper"

// Font configuration
const inter = Inter({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-inter",
})

// Metadata for the entire site
export const metadata: Metadata = {
  title: {
    template: "%s | IFMB 2025",
    default: "IFMB 2025 | International Forum on Maize Biology",
  },
  icons: "@/public/favicon.ico",
  description: "The premier international conference on corn biology research and agricultural innovation.",
  keywords: ["corn biology", "maize biology", "agricultural conference", "IFMB 2025", "plant science"],
  authors: [{ name: "IFMB Organizing Committee" }],
  creator: "Huazhong Agricultural University",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
}

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" className={inter.variable}>
      <head>
        {/* Font Awesome */}
        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
          integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA=="
          crossOrigin="anonymous"
          referrerPolicy="no-referrer"
        />
      </head>
      <body className="font-sans antialiased">
        <LayoutWrapper>{children}</LayoutWrapper>
      </body>
    </html>
  )
}

export default function TestIconsPage() {
  return (
    <div className="min-h-screen bg-white p-8">
      <h1 className="text-2xl font-bold mb-8">FontAwesome Icons Test</h1>
      
      <div className="grid grid-cols-4 gap-4">
        <div className="p-4 border rounded">
          <i className="fas fa-home text-2xl text-blue-500"></i>
          <p className="mt-2">fas fa-home</p>
        </div>
        
        <div className="p-4 border rounded">
          <i className="fas fa-user text-2xl text-green-500"></i>
          <p className="mt-2">fas fa-user</p>
        </div>
        
        <div className="p-4 border rounded">
          <i className="fas fa-bell text-2xl text-yellow-500"></i>
          <p className="mt-2">fas fa-bell</p>
        </div>
        
        <div className="p-4 border rounded">
          <i className="fas fa-cog text-2xl text-gray-500"></i>
          <p className="mt-2">fas fa-cog</p>
        </div>
        
        <div className="p-4 border rounded">
          <i className="fas fa-bars text-2xl text-purple-500"></i>
          <p className="mt-2">fas fa-bars</p>
        </div>
        
        <div className="p-4 border rounded">
          <i className="fas fa-envelope text-2xl text-red-500"></i>
          <p className="mt-2">fas fa-envelope</p>
        </div>
        
        <div className="p-4 border rounded">
          <i className="fas fa-calendar-alt text-2xl text-indigo-500"></i>
          <p className="mt-2">fas fa-calendar-alt</p>
        </div>
        
        <div className="p-4 border rounded">
          <i className="fas fa-credit-card text-2xl text-pink-500"></i>
          <p className="mt-2">fas fa-credit-card</p>
        </div>
      </div>
      
      <div className="mt-8">
        <h2 className="text-xl font-semibold mb-4">CSS Loading Test</h2>
        <p>FontAwesome CSS URL: https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css</p>
        <p>If you can see icons above, FontAwesome is working correctly.</p>
      </div>
    </div>
  )
}

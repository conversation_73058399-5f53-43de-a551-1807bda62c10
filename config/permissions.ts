// 定义不同类型用户可以访问的路径
export const permissionConfig = {
  // 公开路径，所有用户都可以访问
  publicPaths: [
    "/",
    "/login",
    "/register",
    "/forgot-password",
    "/reset-password",
    "/verify-email",

    "/executive",
    "/organization",
    "/reporters",
    "/speakers",
    "/transportation",
  ],

  // 未验证邮箱的非管理员用户可以访问的路径
  unverifiedUserPaths: ["/dashboard", "/profile"],

  // 已验证邮箱的普通用户可以访问的路径
  verifiedUserPaths: [
    "/dashboard",
    "/profile",
  ],

  // 管理员可以访问的路径
  adminPaths: ["/admin", "/admin/users", "/admin/speakers", "/admin/notifications", "/admin/payments", "/admin/content", "/admin/settings"],
}

// 检查路径是否匹配指定的路径列表（支持通配符）
export const isPathInList = (path: string, pathList: string[]): boolean => {
  return pathList.some((allowedPath) => {
    // 精确匹配
    if (allowedPath === path) {
      return true
    }

    // 通配符匹配（例如 /admin/* 匹配所有以 /admin/ 开头的路径）
    if (allowedPath.endsWith("*")) {
      const prefix = allowedPath.slice(0, -1)
      return path.startsWith(prefix)
    }

    return false
  })
}

// 检查用户是否有权限访问特定路径
export const hasPermission = (
  path: string,
  isLoggedIn: boolean,
  isAdmin: boolean,
  isEmailVerified: boolean
): boolean => {
  // 公开路径，所有用户都可以访问
  if (isPathInList(path, permissionConfig.publicPaths)) {
    return true
  }

  // 未登录用户只能访问公开路径
  if (!isLoggedIn) {
    return false
  }

  // 管理员可以访问所有路径
  if (isAdmin) {
    return true
  }

  // 未验证邮箱的用户只能访问有限的路径
  if (!isEmailVerified) {
    return isPathInList(path, permissionConfig.unverifiedUserPaths)
  }

  // 已验证邮箱的普通用户可以访问验证用户路径
  return isPathInList(path, permissionConfig.verifiedUserPaths)
}
